#!/usr/bin/env python3
"""
Complete CORIS API Test Suite
Tests all available API methods including consultation and transactional operations.
Transactional operations run only in HOMOLOG for safety.
"""

from coris_client import CorisClient
from typing import Dict, List
import datetime


def display_simple_result(result: Dict, operation_name: str) -> bool:
    """Display simple result and return success status"""
    if not result:
        print(f"❌ {operation_name}: Failed (no response)")
        return False

    if result.get("success"):
        data_count = len(result.get("data", []))
        print(f"✅ {operation_name}: Success ({data_count} records)")
        return True
    else:
        error = result.get("error", "Unknown error")
        print(f"❌ {operation_name}: Failed - {error}")
        return False


def display_detailed_result(result: Dict, operation_name: str, max_items: int = 3) -> bool:
    """Display detailed result with sample data"""
    if not result:
        print(f"❌ {operation_name}: Failed (no response)")
        return False

    if result.get("success"):
        data = result.get("data", [])
        print(f"✅ {operation_name}: Success ({len(data)} records)")

        # Show sample data
        for i, item in enumerate(data[:max_items], 1):
            if operation_name == "Destinations":
                name = item.get('DESTINO') or item.get('nome', 'N/A')
                id_val = item.get('ID') or item.get('id', 'N/A')
                print(f"   {i}. {name} (ID: {id_val})")
            elif operation_name == "Categories":
                name = item.get('CATEGORIA') or item.get('nome', 'N/A')
                id_val = item.get('ID') or item.get('id', 'N/A')
                print(f"   {i}. {name} (ID: {id_val})")
            elif operation_name == "Coverages":
                name = item.get('COBERTURA') or item.get('nome', 'N/A')
                value = item.get('VALOR') or item.get('valor', 'N/A')
                print(f"   {i}. {name}: {value}")
            elif operation_name == "Individual Prices":
                age = item.get('IDADE') or item.get('idade', 'N/A')
                price = item.get('PRECO') or item.get('preco', 'N/A')
                print(f"   {i}. Age {age}: ${price}")
            elif operation_name == "Family Prices":
                price = item.get('PRECO') or item.get('preco', 'N/A')
                print(f"   {i}. Family: ${price}")
            else:
                # Generic display - try different field name patterns
                key_fields = ['nome', 'NOME', 'id', 'ID', 'valor', 'VALOR', 'preco', 'PRECO', 'status', 'STATUS']
                info = []
                for field in key_fields:
                    if field in item and item[field]:
                        info.append(f"{field.lower()}: {item[field]}")
                        if len(info) >= 2:
                            break
                if info:
                    print(f"   {i}. {' | '.join(info)}")
                else:
                    # Show first few fields if no standard fields found
                    fields = list(item.items())[:2]
                    field_str = " | ".join([f"{k}: {v}" for k, v in fields])
                    print(f"   {i}. {field_str}")

        if len(data) > max_items:
            print(f"   ... and {len(data) - max_items} more")

        return True
    else:
        error = result.get("error", "Unknown error")
        print(f"❌ {operation_name}: Failed - {error}")
        return False


def test_consultation_apis(client: CorisClient, use_prod: bool = True) -> Dict[str, bool]:
    """Test all consultation APIs (safe for production)"""
    env_name = "PRODUCTION" if use_prod else "HOMOLOG"
    print(f"\n{'='*20} Testing Consultation APIs - {env_name} {'='*20}")

    results = {}

    # Test destinations
    print("\n🌍 Testing Destinations API")
    destinations_result = client.search_destinations(use_prod=use_prod)
    results['destinations'] = display_detailed_result(destinations_result, "Destinations")

    # Test categories
    print("\n📋 Testing Categories API")
    categories_result = client.search_categories(use_prod=use_prod)
    results['categories'] = display_detailed_result(categories_result, "Categories")

    # Test exchange rate (may not be available for all users)
    print("\n💱 Testing Exchange Rate API")
    exchange_result = client.get_exchange_rate("USD", use_prod=use_prod)
    if exchange_result and exchange_result.get("status") == "NOK":
        print("⚠️ Exchange Rate: Not available (may require special permissions)")
        results['exchange'] = None  # Mark as not applicable
    else:
        results['exchange'] = display_simple_result(exchange_result, "Exchange Rate")

    # Get a plan ID for detailed tests
    plans_result = client.search_plans(destination=1, use_prod=use_prod)
    plan_id = None
    if plans_result and plans_result.get("success") and plans_result.get("data"):
        plan_id = plans_result["data"][0].get("id")

    if plan_id:
        # Test coverages (may require additional parameters)
        print(f"\n🛡️ Testing Coverages API (Plan ID: {plan_id})")
        coverages_result = client.search_coverages(plan_id, use_prod=use_prod)
        if coverages_result and coverages_result.get("status") == "NOK":
            print("⚠️ Coverages: Not available (may require additional parameters)")
            results['coverages'] = None
        else:
            results['coverages'] = display_detailed_result(coverages_result, "Coverages")

        # Test individual prices (may require additional parameters)
        print(f"\n💰 Testing Individual Prices API (Plan ID: {plan_id}, Age: 30, Days: 15)")
        prices_result = client.search_individual_prices(plan_id, 30, 15, use_prod=use_prod)
        if prices_result and prices_result.get("status") == "NOK":
            print("⚠️ Individual Prices: Not available (may require additional parameters)")
            results['individual_prices'] = None
        else:
            results['individual_prices'] = display_detailed_result(prices_result, "Individual Prices")

        # Test family prices (may require additional parameters)
        print(f"\n👨‍👩‍👧‍👦 Testing Family Prices API (Plan ID: {plan_id}, Ages: 30,25,5, Days: 15)")
        family_result = client.search_family_prices(plan_id, "30,25,5", 15, use_prod=use_prod)
        if family_result and family_result.get("status") == "NOK":
            print("⚠️ Family Prices: Not available (may require additional parameters)")
            results['family_prices'] = None
        else:
            results['family_prices'] = display_detailed_result(family_result, "Family Prices")
    else:
        print("\n⚠️ Skipping detailed tests - no plan ID available")
        results['coverages'] = False
        results['individual_prices'] = False
        results['family_prices'] = False

    # Test voucher consultation (may fail if no vouchers exist)
    print("\n🎫 Testing Voucher Consultation API")
    today = datetime.date.today()
    start_date = (today - datetime.timedelta(days=30)).strftime("%Y-%m-%d")
    end_date = today.strftime("%Y-%m-%d")

    voucher_period_result = client.consult_voucher_period(start_date, end_date, use_prod=use_prod)
    results['voucher_period'] = display_simple_result(voucher_period_result, "Voucher Period")

    return results


def test_transactional_apis(client: CorisClient) -> Dict[str, bool]:
    """Test transactional APIs (HOMOLOG ONLY for safety)"""
    print(f"\n{'='*20} Testing Transactional APIs - HOMOLOG ONLY {'='*20}")

    results = {}

    # Get a plan for testing
    plans_result = client.search_plans(destination=1, use_prod=False)
    plan_id = None
    if plans_result and plans_result.get("success") and plans_result.get("data"):
        plan_id = plans_result["data"][0].get("id")

    if not plan_id:
        print("❌ Cannot test transactional APIs - no plan ID available")
        return {"voucher_creation": False, "voucher_cancellation": False}

    # Test voucher creation
    print(f"\n🎫 Testing Voucher Creation API (Plan ID: {plan_id})")

    # Sample passenger data
    passenger_data = {
        "name": "João Silva Teste",
        "email": "<EMAIL>",
        "phone": "+5511999999999",
        "birth_date": "1990-01-01"
    }

    # Sample travel data
    travel_data = {
        "travel_date": "2024-06-01",
        "return_date": "2024-06-15",
        "destination": "Argentina"
    }

    voucher_result = client.create_individual_voucher(
        plan_id, passenger_data, travel_data, use_prod=False
    )
    results['voucher_creation'] = display_simple_result(voucher_result, "Voucher Creation")

    # If voucher was created, try to get its ID for cancellation test
    voucher_id = None
    if voucher_result and voucher_result.get("success") and voucher_result.get("data"):
        # Try to extract voucher ID from response
        voucher_data = voucher_result["data"][0] if voucher_result["data"] else {}
        voucher_id = voucher_data.get("idvoucher") or voucher_data.get("id")

    # Test voucher cancellation (only if we have a voucher ID)
    if voucher_id:
        print(f"\n❌ Testing Voucher Cancellation API (Voucher ID: {voucher_id})")
        cancel_result = client.cancel_voucher(voucher_id, "Teste automatizado", use_prod=False)
        results['voucher_cancellation'] = display_simple_result(cancel_result, "Voucher Cancellation")
    else:
        print("\n⚠️ Skipping voucher cancellation test - no voucher ID available")
        results['voucher_cancellation'] = False

    return results


def main():
    """Main test function"""
    print("🔐 Complete CORIS API Test Suite")
    print("="*80)

    try:
        # Initialize client
        client = CorisClient()
        print(f"Initialized client for user: {client.login}")

        # Test basic authentication first
        print("\n🔑 Testing Basic Authentication")
        auth_result = client.validate_access(use_prod=True)
        if not auth_result or not auth_result.get("success"):
            print("❌ Authentication failed. Cannot proceed with tests.")
            return

        user_data = auth_result["data"][0]
        print(f"✅ Authentication successful for user {user_data['login']} (ID: {user_data['idcliente']})")

        # Test consultation APIs in both environments
        homolog_consultation = test_consultation_apis(client, use_prod=False)
        prod_consultation = test_consultation_apis(client, use_prod=True)

        # Test transactional APIs (HOMOLOG only)
        transactional_results = test_transactional_apis(client)

        # Summary
        print("\n" + "="*80)
        print("📊 COMPLETE TEST SUMMARY")
        print("="*80)

        print("\nConsultation APIs - HOMOLOG:")
        for api, success in homolog_consultation.items():
            if success is None:
                status = "⚠️ N/A"
            elif success:
                status = "✅ PASS"
            else:
                status = "❌ FAIL"
            print(f"  {api.replace('_', ' ').title():<20}: {status}")

        print("\nConsultation APIs - PRODUCTION:")
        for api, success in prod_consultation.items():
            if success is None:
                status = "⚠️ N/A"
            elif success:
                status = "✅ PASS"
            else:
                status = "❌ FAIL"
            print(f"  {api.replace('_', ' ').title():<20}: {status}")

        print("\nTransactional APIs - HOMOLOG ONLY:")
        for api, success in transactional_results.items():
            status = "✅ PASS" if success else "❌ FAIL"
            print(f"  {api.replace('_', ' ').title():<20}: {status}")

        # Overall assessment
        all_consultation_passed = all(homolog_consultation.values()) and all(prod_consultation.values())
        some_transactional_passed = any(transactional_results.values())

        print(f"\n{'='*80}")
        if all_consultation_passed:
            print("🎉 All consultation APIs are working correctly in both environments!")
        else:
            print("⚠️ Some consultation APIs failed. Check details above.")

        if some_transactional_passed:
            print("✅ Transactional APIs are accessible (tested in HOMOLOG only).")
        else:
            print("⚠️ Transactional APIs may need additional parameters or permissions.")

    except ValueError as e:
        print(f"❌ Configuration error: {e}")
        print("Please check your .env file and ensure all required variables are set.")
    except Exception as e:
        print(f"❌ Unexpected error: {e}")


if __name__ == "__main__":
    main()
