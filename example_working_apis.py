#!/usr/bin/env python3
"""
CORIS API Working Examples
Demonstrates the APIs that are confirmed to work with the current credentials.
"""

from coris_client import CorisClient
import j<PERSON>


def example_working_consultation_apis():
    """Examples of consultation APIs that work"""
    print("🔍 Working Consultation APIs Examples")
    print("="*50)
    
    client = CorisClient()
    
    # 1. Authentication
    print("\n1. 🔑 User Authentication")
    auth_result = client.validate_access(use_prod=True)
    if auth_result and auth_result.get("success"):
        user_data = auth_result["data"][0]
        print(f"✅ Authenticated as: {user_data['login']} (Client ID: {user_data['idcliente']})")
    
    # 2. Search Destinations
    print("\n2. 🌍 Available Destinations")
    destinations = client.search_destinations(use_prod=True)
    if destinations and destinations.get("success"):
        print("Available destinations:")
        for dest in destinations["data"]:
            print(f"   • {dest['DESTINO']} (ID: {dest['ID']})")
    
    # 3. Search Categories
    print("\n3. 📋 Available Categories")
    categories = client.search_categories(use_prod=True)
    if categories and categories.get("success"):
        print("Available categories:")
        for cat in categories["data"]:
            print(f"   • {cat['CATEGORIA']} (ID: {cat['ID']})")
    
    # 4. Search Plans for Different Destinations
    print("\n4. 🎯 Travel Plans by Destination")
    
    destinations_to_test = [
        (1, "América Latina"),
        (2, "Brasil"),
        (4, "Mundo (Exceto EUA)"),
        (5, "Mundo (Incluindo EUA)")
    ]
    
    for dest_id, dest_name in destinations_to_test:
        plans = client.search_plans(destination=dest_id, use_prod=True)
        if plans and plans.get("success"):
            plan_count = len(plans["data"])
            print(f"   • {dest_name}: {plan_count} plans available")
            
            # Show cheapest plan
            if plans["data"]:
                cheapest = min(plans["data"], key=lambda x: float(x.get('preco', '999').replace(',', '.')))
                print(f"     Cheapest: {cheapest['nome']} - ${cheapest['preco']}")
        else:
            print(f"   • {dest_name}: No plans available")


def example_plan_analysis():
    """Analyze available plans in detail"""
    print("\n\n📊 Detailed Plan Analysis")
    print("="*50)
    
    client = CorisClient()
    
    # Get all plans for Latin America
    plans = client.search_plans(destination=1, use_prod=True)
    if not plans or not plans.get("success"):
        print("❌ Could not retrieve plans for analysis")
        return
    
    plans_data = plans["data"]
    print(f"Analyzing {len(plans_data)} plans for Latin America:")
    
    # Group by plan type
    plan_types = {}
    for plan in plans_data:
        name = plan['nome']
        plan_type = name.split()[0]  # BASIC, MAX, VIP
        if plan_type not in plan_types:
            plan_types[plan_type] = []
        plan_types[plan_type].append(plan)
    
    print(f"\nPlan types available: {', '.join(plan_types.keys())}")
    
    for plan_type, type_plans in plan_types.items():
        print(f"\n{plan_type} Plans ({len(type_plans)} available):")
        
        # Sort by coverage amount (extract number from name)
        sorted_plans = sorted(type_plans, key=lambda x: extract_coverage_amount(x['nome']))
        
        for plan in sorted_plans:
            coverage = extract_coverage_from_name(plan['nome'])
            print(f"   • {coverage:<20} | ${plan['preco']:<6} | Family: ${plan['precofamiliar']}")


def extract_coverage_amount(plan_name):
    """Extract coverage amount from plan name for sorting"""
    try:
        # Extract number from plan name (e.g., "BASIC 30 AMERICA LATINA" -> 30)
        parts = plan_name.split()
        for part in parts:
            if part.isdigit():
                return int(part)
            elif 'KK' in part:
                return 1000  # 1KK = 1000
        return 0
    except:
        return 0


def extract_coverage_from_name(plan_name):
    """Extract coverage description from plan name"""
    parts = plan_name.split()
    if len(parts) >= 2:
        return f"{parts[0]} {parts[1]}"
    return plan_name


def example_environment_comparison():
    """Compare results between homolog and production"""
    print("\n\n🔄 Environment Comparison")
    print("="*50)
    
    client = CorisClient()
    
    print("Comparing plan availability between environments:")
    
    # Test same query in both environments
    homolog_plans = client.search_plans(destination=1, use_prod=False)
    prod_plans = client.search_plans(destination=1, use_prod=True)
    
    homolog_count = len(homolog_plans["data"]) if homolog_plans and homolog_plans.get("success") else 0
    prod_count = len(prod_plans["data"]) if prod_plans and prod_plans.get("success") else 0
    
    print(f"   • Homolog: {homolog_count} plans")
    print(f"   • Production: {prod_count} plans")
    
    if homolog_count == prod_count and homolog_count > 0:
        print("   ✅ Both environments return the same number of plans")
    elif homolog_count > 0 and prod_count > 0:
        print("   ⚠️ Different number of plans between environments")
    else:
        print("   ❌ One or both environments failed")


def example_api_response_structure():
    """Show the structure of API responses"""
    print("\n\n📋 API Response Structure Examples")
    print("="*50)
    
    client = CorisClient()
    
    # Show structure of different API responses
    print("\n1. Authentication Response:")
    auth = client.validate_access(use_prod=True)
    if auth and auth.get("success"):
        print(json.dumps(auth, indent=2, ensure_ascii=False))
    
    print("\n2. Destinations Response (first item):")
    dest = client.search_destinations(use_prod=True)
    if dest and dest.get("success") and dest["data"]:
        sample_dest = {"sample_destination": dest["data"][0]}
        print(json.dumps(sample_dest, indent=2, ensure_ascii=False))
    
    print("\n3. Plans Response (first item):")
    plans = client.search_plans(destination=1, use_prod=True)
    if plans and plans.get("success") and plans["data"]:
        sample_plan = {"sample_plan": plans["data"][0]}
        print(json.dumps(sample_plan, indent=2, ensure_ascii=False))


def main():
    """Run all working API examples"""
    print("🌟 CORIS API - Working Examples")
    print("="*60)
    
    try:
        example_working_consultation_apis()
        example_plan_analysis()
        example_environment_comparison()
        example_api_response_structure()
        
        print("\n" + "="*60)
        print("✅ All working API examples completed successfully!")
        print("\nSummary of working APIs:")
        print("   • ✅ User Authentication (validarAcesso)")
        print("   • ✅ Search Destinations (BuscarDestinos)")
        print("   • ✅ Search Categories (BuscarCategorias)")
        print("   • ✅ Search Plans (BuscarPlanosNovosV13)")
        print("   • ⚠️ Other APIs may require additional parameters or permissions")
        
    except Exception as e:
        print(f"❌ Error running examples: {e}")


if __name__ == "__main__":
    main()
