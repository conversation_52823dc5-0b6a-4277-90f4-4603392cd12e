#!/usr/bin/env python3
"""
CORIS API Usage Examples
Demonstrates how to use the CorisClient for common operations.
"""

from coris_client import CorisClient


def example_basic_usage():
    """Basic usage example"""
    print("=== Basic Usage Example ===")
    
    # Initialize client (uses environment variables from .env)
    client = CorisClient()
    
    # Validate login
    print("1. Validating login...")
    login_result = client.validate_access(use_prod=True)
    
    if login_result and login_result.get("success"):
        user_data = login_result["data"][0]
        print(f"✅ Login successful for user {user_data['login']} (ID: {user_data['idcliente']})")
    else:
        print("❌ Login failed")
        return
    
    # Search for plans
    print("\n2. Searching for travel plans...")
    plans_result = client.search_plans(
        destination=1,      # Latin America
        duration_type=1,    # Single trip
        home_country=0,     # Not home country
        multi_trip=0,       # Single trip
        use_prod=True
    )
    
    if plans_result and plans_result.get("success"):
        plans = plans_result["data"]
        print(f"✅ Found {len(plans)} plans")
        
        # Show top 3 cheapest plans
        sorted_plans = sorted(plans, key=lambda x: float(x.get('preco', '999').replace(',', '.')))
        print("\nTop 3 cheapest plans:")
        for i, plan in enumerate(sorted_plans[:3], 1):
            print(f"  {i}. {plan['nome']} - ${plan['preco']}")
    else:
        print("❌ Failed to retrieve plans")


def example_custom_credentials():
    """Example with custom credentials"""
    print("\n=== Custom Credentials Example ===")
    
    # Initialize client with custom credentials
    client = CorisClient(
        login="your_login",
        password="your_password",
        homolog_url="https://ws.coris-homolog.com.br/webservice2/service.asmx",
        prod_url="https://ws.coris.com.br/webservice2/service.asmx"
    )
    
    print("Client initialized with custom credentials")


def example_error_handling():
    """Example with error handling"""
    print("\n=== Error Handling Example ===")
    
    try:
        client = CorisClient()
        
        # Try to validate access
        result = client.validate_access(use_prod=True)
        
        if result is None:
            print("❌ Request failed completely")
        elif not result.get("success"):
            print(f"❌ API returned error: {result.get('error', 'Unknown error')}")
        else:
            print("✅ Success!")
            
    except ValueError as e:
        print(f"❌ Configuration error: {e}")
    except Exception as e:
        print(f"❌ Unexpected error: {e}")


def example_different_destinations():
    """Example searching plans for different destinations"""
    print("\n=== Different Destinations Example ===")
    
    client = CorisClient()
    
    destinations = {
        1: "Latin America",
        2: "Europe", 
        3: "North America",
        4: "Asia",
        5: "Africa",
        6: "Oceania"
    }
    
    print("Searching plans for different destinations:")
    
    for dest_code, dest_name in destinations.items():
        result = client.search_plans(destination=dest_code, use_prod=True)
        
        if result and result.get("success"):
            plan_count = len(result["data"])
            print(f"  {dest_name}: {plan_count} plans available")
        else:
            print(f"  {dest_name}: No plans or error")


def main():
    """Run all examples"""
    print("🌎 CORIS API Usage Examples")
    print("="*50)
    
    try:
        example_basic_usage()
        example_custom_credentials()
        example_error_handling()
        example_different_destinations()
        
        print("\n" + "="*50)
        print("✅ All examples completed!")
        
    except Exception as e:
        print(f"❌ Error running examples: {e}")


if __name__ == "__main__":
    main()
