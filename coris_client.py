"""
CORIS API Client - Clean production-ready version
A simple client for interacting with the CORIS travel insurance API.
"""

import os
import requests
import html
from xml.etree import ElementTree as ET
from dotenv import load_dotenv
from typing import Dict, List, Optional, Tuple

load_dotenv()


class CorisClient:
    """CORIS API Client for travel insurance operations"""

    def __init__(self, login: str = None, password: str = None,
                 homolog_url: str = None, prod_url: str = None):
        """
        Initialize CORIS client

        Args:
            login: CORIS login (defaults to CORIS_LOGIN env var)
            password: CORIS password (defaults to CORIS_SENHA env var)
            homolog_url: Homolog environment URL (defaults to URL_HOMO env var)
            prod_url: Production environment URL (defaults to URL_PROD env var)
        """
        self.login = login or os.getenv("CORIS_LOGIN")
        self.password = password or os.getenv("CORIS_SENHA")
        self.homolog_url = homolog_url or os.getenv("URL_HOMO")
        self.prod_url = prod_url or os.getenv("URL_PROD")

        if not self.login or not self.password:
            raise ValueError("Login and password are required")
        if not self.homolog_url or not self.prod_url:
            raise ValueError("Both homolog and production URLs are required")

    def _build_xml_params(self, params: Dict[str, Tuple[str, str]]) -> str:
        """Build XML parameter string for the strXML parameter"""
        inner = "\n".join(
            f"<param name='{k}' type='{t}' value='{v}' />"
            for k, (t, v) in params.items()
        )
        return f"<execute>{inner}</execute>"

    def _build_soap_body(self, method: str, xml_params: str) -> str:
        """Build SOAP envelope for a specific method"""
        escaped_xml = html.escape(xml_params)
        return f"""<?xml version="1.0" encoding="utf-8"?>
<soap:Envelope xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
               xmlns:xsd="http://www.w3.org/2001/XMLSchema"
               xmlns:soap="http://schemas.xmlsoap.org/soap/envelope/">
  <soap:Body>
    <{method} xmlns="http://tempuri.org/">
      <strXML>{escaped_xml}</strXML>
    </{method}>
  </soap:Body>
</soap:Envelope>"""

    def _parse_response(self, response_text: str, method: str) -> Dict:
        """Parse CORIS SOAP response and extract meaningful data"""
        try:
            # Parse the SOAP envelope
            root = ET.fromstring(response_text)

            # Find the result element
            result_elem = root.find(f".//{{http://tempuri.org/}}{method}Result")
            if result_elem is None:
                return {"error": "Result element not found", "raw": response_text}

            # The result contains escaped XML, so we need to unescape it
            result_xml = html.unescape(result_elem.text)

            # Remove XML declaration if present
            if result_xml.startswith('<?xml'):
                result_xml = result_xml[result_xml.find('?>') + 2:].strip()

            # Wrap in a root element since the XML may have multiple root elements
            wrapped_xml = f"<root>{result_xml}</root>"

            # Parse the inner XML
            inner_root = ET.fromstring(wrapped_xml)

            # Extract result status
            result_status = inner_root.find("result")
            status = result_status.text if result_status is not None else "UNKNOWN"

            parsed_data = {
                "status": status,
                "success": status == "OK",
                "data": []
            }

            # Extract table data if present
            table = inner_root.find("table")
            if table is not None:
                for row in table.findall("row"):
                    row_data = {}
                    for column in row.findall("column"):
                        name = column.get("name")
                        value = column.text
                        row_data[name] = value
                    parsed_data["data"].append(row_data)

            return parsed_data

        except ET.ParseError as e:
            return {"error": f"XML parsing error: {e}", "raw": response_text}
        except Exception as e:
            return {"error": f"Unexpected error: {e}", "raw": response_text}

    def _call_method(self, method: str, params: Dict[str, Tuple[str, str]],
                     use_prod: bool = True) -> Optional[Dict]:
        """Call a specific SOAP method with parameters"""
        xml_params = self._build_xml_params(params)
        body = self._build_soap_body(method, xml_params)
        headers = {
            "Content-Type": "text/xml; charset=utf-8",
            "SOAPAction": f"\"http://tempuri.org/{method}\""
        }

        url = self.prod_url if use_prod else self.homolog_url

        try:
            resp = requests.post(url, data=body, headers=headers, timeout=30)
            resp.raise_for_status()

            # Parse the response
            parsed = self._parse_response(resp.text, method)
            return parsed

        except requests.RequestException as e:
            return {"error": f"Request error: {e}", "success": False}
        except Exception as e:
            return {"error": f"Unexpected error: {e}", "success": False}

    def validate_access(self, use_prod: bool = True) -> Optional[Dict]:
        """
        Validate user access credentials

        Args:
            use_prod: Use production environment (default: True)

        Returns:
            Dict with validation result or None if failed
        """
        return self._call_method("validarAcesso", {
            "login": ("varchar", self.login),
            "senha": ("varchar", self.password),
            "cartao": ("char", "M")
        }, use_prod)

    def verify_access(self, use_prod: bool = True) -> Optional[Dict]:
        """
        Verify user access (alternative method)

        Args:
            use_prod: Use production environment (default: True)

        Returns:
            Dict with verification result or None if failed
        """
        return self._call_method("VerificaAcesso", {
            "login": ("varchar", self.login),
            "senha": ("varchar", self.password)
        }, use_prod)

    def search_plans(self, destination: int = 1, duration_type: int = 1,
                     home_country: int = 0, multi_trip: int = 0,
                     use_prod: bool = True) -> Optional[Dict]:
        """
        Search for travel insurance plans

        Args:
            destination: Destination code (1=Latin America, etc.)
            duration_type: Duration type (1=single trip, etc.)
            home_country: Home country flag (0=not home country)
            multi_trip: Multi-trip flag (0=single trip)
            use_prod: Use production environment (default: True)

        Returns:
            Dict with plans data or None if failed
        """
        return self._call_method("BuscarPlanosNovosV13", {
            "login": ("varchar", self.login),
            "senha": ("varchar", self.password),
            "destino": ("int", str(destination)),
            "vigencia": ("int", str(duration_type)),
            "home": ("int", str(home_country)),
            "multi": ("int", str(multi_trip))
        }, use_prod)

    # === CONSULTATION METHODS (Safe for production) ===

    def search_destinations(self, use_prod: bool = True) -> Optional[Dict]:
        """Search available destinations"""
        return self._call_method("BuscarDestinos", {
            "login": ("varchar", self.login),
            "senha": ("varchar", self.password)
        }, use_prod)

    def search_categories(self, use_prod: bool = True) -> Optional[Dict]:
        """Search available categories"""
        return self._call_method("BuscarCategorias", {
            "login": ("varchar", self.login),
            "senha": ("varchar", self.password)
        }, use_prod)

    def search_coverages(self, plan_id: str, use_prod: bool = True) -> Optional[Dict]:
        """Search coverages for a specific plan"""
        return self._call_method("buscarCoberturas", {
            "login": ("varchar", self.login),
            "senha": ("varchar", self.password),
            "idplano": ("varchar", plan_id)
        }, use_prod)

    def search_individual_prices(self, plan_id: str, age: int, days: int,
                               use_prod: bool = True) -> Optional[Dict]:
        """Search individual prices for a plan"""
        return self._call_method("BuscarPrecosIndividualV13", {
            "login": ("varchar", self.login),
            "senha": ("varchar", self.password),
            "idplano": ("varchar", plan_id),
            "idade": ("int", str(age)),
            "dias": ("int", str(days))
        }, use_prod)

    def search_family_prices(self, plan_id: str, ages: str, days: int,
                           use_prod: bool = True) -> Optional[Dict]:
        """Search family prices for a plan"""
        return self._call_method("BuscarPrecosFamiliarV13", {
            "login": ("varchar", self.login),
            "senha": ("varchar", self.password),
            "idplano": ("varchar", plan_id),
            "idades": ("varchar", ages),  # e.g., "30,25,5,3"
            "dias": ("int", str(days))
        }, use_prod)

    def consult_voucher(self, voucher_id: str, use_prod: bool = True) -> Optional[Dict]:
        """Consult an existing voucher"""
        return self._call_method("consultarVoucher", {
            "login": ("varchar", self.login),
            "senha": ("varchar", self.password),
            "idvoucher": ("varchar", voucher_id)
        }, use_prod)

    def consult_voucher_period(self, start_date: str, end_date: str,
                             use_prod: bool = True) -> Optional[Dict]:
        """Consult vouchers in a date period"""
        return self._call_method("consultarVoucherPeriodo", {
            "login": ("varchar", self.login),
            "senha": ("varchar", self.password),
            "datainicio": ("varchar", start_date),  # Format: YYYY-MM-DD
            "datafim": ("varchar", end_date)
        }, use_prod)

    def get_exchange_rate(self, currency: str = "USD", use_prod: bool = True) -> Optional[Dict]:
        """Get currency exchange rate"""
        return self._call_method("cambioValor", {
            "login": ("varchar", self.login),
            "senha": ("varchar", self.password),
            "moeda": ("varchar", currency)
        }, use_prod)

    # === TRANSACTIONAL METHODS (Homolog only for safety) ===

    def create_individual_voucher(self, plan_id: str, passenger_data: Dict,
                                travel_data: Dict, use_prod: bool = False) -> Optional[Dict]:
        """Create an individual voucher (HOMOLOG ONLY by default)"""
        if use_prod:
            print("⚠️ WARNING: Creating voucher in PRODUCTION environment!")

        return self._call_method("InsereVoucherIndividualV13", {
            "login": ("varchar", self.login),
            "senha": ("varchar", self.password),
            "idplano": ("varchar", plan_id),
            "nome": ("varchar", passenger_data.get("name", "")),
            "email": ("varchar", passenger_data.get("email", "")),
            "telefone": ("varchar", passenger_data.get("phone", "")),
            "datanascimento": ("varchar", passenger_data.get("birth_date", "")),
            "dataviagem": ("varchar", travel_data.get("travel_date", "")),
            "dataretorno": ("varchar", travel_data.get("return_date", "")),
            "destino": ("varchar", travel_data.get("destination", ""))
        }, use_prod)

    def cancel_voucher(self, voucher_id: str, reason: str = "Cancelamento solicitado",
                      use_prod: bool = False) -> Optional[Dict]:
        """Cancel a voucher (HOMOLOG ONLY by default)"""
        if use_prod:
            print("⚠️ WARNING: Canceling voucher in PRODUCTION environment!")

        return self._call_method("CancelamentoVoucherBD", {
            "login": ("varchar", self.login),
            "senha": ("varchar", self.password),
            "idvoucher": ("varchar", voucher_id),
            "motivo": ("varchar", reason)
        }, use_prod)