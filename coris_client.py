"""
CORIS API Client - Clean production-ready version
A simple client for interacting with the CORIS travel insurance API.
"""

import os
import requests
import html
from xml.etree import ElementTree as ET
from dotenv import load_dotenv
from typing import Dict, List, Optional, Tuple

load_dotenv()


class CorisClient:
    """CORIS API Client for travel insurance operations"""
    
    def __init__(self, login: str = None, password: str = None, 
                 homolog_url: str = None, prod_url: str = None):
        """
        Initialize CORIS client
        
        Args:
            login: CORIS login (defaults to CORIS_LOGIN env var)
            password: CORIS password (defaults to CORIS_SENHA env var)
            homolog_url: Homolog environment URL (defaults to URL_HOMO env var)
            prod_url: Production environment URL (defaults to URL_PROD env var)
        """
        self.login = login or os.getenv("CORIS_LOGIN")
        self.password = password or os.getenv("CORIS_SENHA")
        self.homolog_url = homolog_url or os.getenv("URL_HOMO")
        self.prod_url = prod_url or os.getenv("URL_PROD")
        
        if not self.login or not self.password:
            raise ValueError("Login and password are required")
        if not self.homolog_url or not self.prod_url:
            raise ValueError("Both homolog and production URLs are required")
    
    def _build_xml_params(self, params: Dict[str, Tuple[str, str]]) -> str:
        """Build XML parameter string for the strXML parameter"""
        inner = "\n".join(
            f"<param name='{k}' type='{t}' value='{v}' />"
            for k, (t, v) in params.items()
        )
        return f"<execute>{inner}</execute>"
    
    def _build_soap_body(self, method: str, xml_params: str) -> str:
        """Build SOAP envelope for a specific method"""
        escaped_xml = html.escape(xml_params)
        return f"""<?xml version="1.0" encoding="utf-8"?>
<soap:Envelope xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
               xmlns:xsd="http://www.w3.org/2001/XMLSchema"
               xmlns:soap="http://schemas.xmlsoap.org/soap/envelope/">
  <soap:Body>
    <{method} xmlns="http://tempuri.org/">
      <strXML>{escaped_xml}</strXML>
    </{method}>
  </soap:Body>
</soap:Envelope>"""
    
    def _parse_response(self, response_text: str, method: str) -> Dict:
        """Parse CORIS SOAP response and extract meaningful data"""
        try:
            # Parse the SOAP envelope
            root = ET.fromstring(response_text)
            
            # Find the result element
            result_elem = root.find(f".//{{http://tempuri.org/}}{method}Result")
            if result_elem is None:
                return {"error": "Result element not found", "raw": response_text}
            
            # The result contains escaped XML, so we need to unescape it
            result_xml = html.unescape(result_elem.text)
            
            # Remove XML declaration if present
            if result_xml.startswith('<?xml'):
                result_xml = result_xml[result_xml.find('?>') + 2:].strip()
            
            # Wrap in a root element since the XML may have multiple root elements
            wrapped_xml = f"<root>{result_xml}</root>"
            
            # Parse the inner XML
            inner_root = ET.fromstring(wrapped_xml)
            
            # Extract result status
            result_status = inner_root.find("result")
            status = result_status.text if result_status is not None else "UNKNOWN"
            
            parsed_data = {
                "status": status,
                "success": status == "OK",
                "data": []
            }
            
            # Extract table data if present
            table = inner_root.find("table")
            if table is not None:
                for row in table.findall("row"):
                    row_data = {}
                    for column in row.findall("column"):
                        name = column.get("name")
                        value = column.text
                        row_data[name] = value
                    parsed_data["data"].append(row_data)
            
            return parsed_data
            
        except ET.ParseError as e:
            return {"error": f"XML parsing error: {e}", "raw": response_text}
        except Exception as e:
            return {"error": f"Unexpected error: {e}", "raw": response_text}
    
    def _call_method(self, method: str, params: Dict[str, Tuple[str, str]], 
                     use_prod: bool = True) -> Optional[Dict]:
        """Call a specific SOAP method with parameters"""
        xml_params = self._build_xml_params(params)
        body = self._build_soap_body(method, xml_params)
        headers = {
            "Content-Type": "text/xml; charset=utf-8",
            "SOAPAction": f"\"http://tempuri.org/{method}\""
        }
        
        url = self.prod_url if use_prod else self.homolog_url
        
        try:
            resp = requests.post(url, data=body, headers=headers, timeout=30)
            resp.raise_for_status()
            
            # Parse the response
            parsed = self._parse_response(resp.text, method)
            return parsed
            
        except requests.RequestException as e:
            return {"error": f"Request error: {e}", "success": False}
        except Exception as e:
            return {"error": f"Unexpected error: {e}", "success": False}
    
    def validate_access(self, use_prod: bool = True) -> Optional[Dict]:
        """
        Validate user access credentials
        
        Args:
            use_prod: Use production environment (default: True)
            
        Returns:
            Dict with validation result or None if failed
        """
        return self._call_method("validarAcesso", {
            "login": ("varchar", self.login),
            "senha": ("varchar", self.password),
            "cartao": ("char", "M")
        }, use_prod)
    
    def verify_access(self, use_prod: bool = True) -> Optional[Dict]:
        """
        Verify user access (alternative method)
        
        Args:
            use_prod: Use production environment (default: True)
            
        Returns:
            Dict with verification result or None if failed
        """
        return self._call_method("VerificaAcesso", {
            "login": ("varchar", self.login),
            "senha": ("varchar", self.password)
        }, use_prod)
    
    def search_plans(self, destination: int = 1, duration_type: int = 1, 
                     home_country: int = 0, multi_trip: int = 0, 
                     use_prod: bool = True) -> Optional[Dict]:
        """
        Search for travel insurance plans
        
        Args:
            destination: Destination code (1=Latin America, etc.)
            duration_type: Duration type (1=single trip, etc.)
            home_country: Home country flag (0=not home country)
            multi_trip: Multi-trip flag (0=single trip)
            use_prod: Use production environment (default: True)
            
        Returns:
            Dict with plans data or None if failed
        """
        return self._call_method("BuscarPlanosNovosV13", {
            "login": ("varchar", self.login),
            "senha": ("varchar", self.password),
            "destino": ("int", str(destination)),
            "vigencia": ("int", str(duration_type)),
            "home": ("int", str(home_country)),
            "multi": ("int", str(multi_trip))
        }, use_prod)
