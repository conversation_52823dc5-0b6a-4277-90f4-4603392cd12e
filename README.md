# CORIS API Client

A Python client for interacting with the CORIS travel insurance API. This project provides both a clean, production-ready client library and comprehensive test suites.

## 🚀 Quick Start

1. **Install dependencies:**
   ```bash
   pip install requests python-dotenv
   ```

2. **Set up environment variables:**
   Create a `.env` file with your CORIS credentials:
   ```env
   CORIS_LOGIN=your_login
   CORIS_SENHA=your_password
   URL_HOMO=https://ws.coris-homolog.com.br/webservice2/service.asmx
   URL_PROD=https://ws.coris.com.br/webservice2/service.asmx
   ```

3. **Run the test suite:**
   ```bash
   python test_coris_clean.py
   ```

## 📁 Files Overview

### Core Files
- **`coris_client.py`** - Clean, production-ready CORIS API client class
- **`test_coris_clean.py`** - Comprehensive test suite with readable output
- **`example_usage.py`** - Usage examples and common patterns

### Legacy Files
- **`test_coris.py`** - Original working test file (more verbose)
- **`coris_api_test.py`** - Alternative test implementation

### Configuration
- **`.env`** - Environment variables (credentials and URLs)
- **`manual-coris.pdf`** - API documentation (if available)

## 🔧 Usage

### Basic Usage

```python
from coris_client import CorisClient

# Initialize client (uses .env file)
client = CorisClient()

# Validate login
result = client.validate_access(use_prod=True)
if result and result.get("success"):
    print("✅ Login successful!")
    user_data = result["data"][0]
    print(f"User: {user_data['login']}, ID: {user_data['idcliente']}")

# Search for travel plans
plans = client.search_plans(destination=1, use_prod=True)
if plans and plans.get("success"):
    print(f"Found {len(plans['data'])} plans")
    for plan in plans["data"]:
        print(f"- {plan['nome']}: ${plan['preco']}")
```

### Custom Configuration

```python
# Initialize with custom credentials
client = CorisClient(
    login="your_login",
    password="your_password",
    homolog_url="https://ws.coris-homolog.com.br/webservice2/service.asmx",
    prod_url="https://ws.coris.com.br/webservice2/service.asmx"
)
```

## 🧪 Testing

### Run All Tests
```bash
python test_coris_clean.py
```

### Run Original Tests
```bash
python test_coris.py
```

### Run Examples
```bash
python example_usage.py
```

## 📊 API Methods

### `validate_access(use_prod=True)`
Validates user login credentials.

**Returns:**
```python
{
    "status": "OK",
    "success": True,
    "data": [
        {
            "erro": "0",
            "login": "rs20427",
            "idcliente": "20427"
        }
    ]
}
```

### `verify_access(use_prod=True)`
Alternative method to verify user access.

### `search_plans(destination=1, duration_type=1, home_country=0, multi_trip=0, use_prod=True)`
Search for travel insurance plans.

**Parameters:**
- `destination`: Destination code (1=Latin America, 2=Europe, etc.)
- `duration_type`: Duration type (1=single trip)
- `home_country`: Home country flag (0=not home country)
- `multi_trip`: Multi-trip flag (0=single trip)

**Returns:**
```python
{
    "status": "OK",
    "success": True,
    "data": [
        {
            "id": "23384",
            "nome": "BASIC 15 AMERICA LATINA",
            "preco": "4",
            "precofamiliar": "3",
            "idademaximaparaemissao": "85",
            "vigenciamaxima": "540"
        },
        # ... more plans
    ]
}
```

## 🔍 Troubleshooting

### Common Issues

1. **Missing credentials error:**
   - Ensure `.env` file exists with correct variable names
   - Check that `CORIS_LOGIN` and `CORIS_SENHA` are set

2. **Connection errors:**
   - Verify URLs in `.env` file
   - Check internet connection
   - Try both homolog and production environments

3. **Authentication failures:**
   - Verify credentials are correct
   - Check if account is active

### Debug Mode

For detailed debugging, you can modify the client to show raw responses:

```python
# In coris_client.py, add verbose parameter to _call_method
result = client._call_method("validarAcesso", params, use_prod=True)
print("Raw response:", result.get("raw", "No raw data"))
```

## 🎯 Test Results

When all tests pass, you should see:

```
🎉 All tests passed in both environments! CORIS API is working correctly.

HOMOLOG Environment:
  Login Validation:     ✅ PASS
  Access Verification:  ✅ PASS
  Plans Search:         ✅ PASS

PRODUCTION Environment:
  Login Validation:     ✅ PASS
  Access Verification:  ✅ PASS
  Plans Search:         ✅ PASS
```

## 📝 Notes

- The client automatically handles SOAP envelope creation and XML parsing
- Both homolog and production environments are supported
- Error handling includes both HTTP errors and API-level errors
- The client properly escapes XML content to prevent parsing issues
- All responses are parsed into Python dictionaries for easy access

## 🤝 Contributing

Feel free to submit issues or pull requests to improve the client or add new functionality.
