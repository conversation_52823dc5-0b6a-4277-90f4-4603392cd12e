#!/usr/bin/env python3
"""
Clean CORIS API Test Suite
Tests login validation and plan search functionality using the CorisClient.
"""

from coris_client import CorisClient
from typing import Dict


def display_login_result(result: Dict) -> None:
    """Display login result in a readable format"""
    if not result or not result.get("success"):
        print("❌ Login failed")
        if result and "error" in result:
            print(f"   Error: {result['error']}")
        return
    
    data = result.get("data", [])
    if data:
        user_data = data[0]
        print("✅ Login successful!")
        print(f"   User: {user_data.get('login', 'N/A')}")
        print(f"   Client ID: {user_data.get('idcliente', 'N/A')}")
        print(f"   Error Code: {user_data.get('erro', 'N/A')}")


def display_plans_result(result: Dict) -> None:
    """Display plans result in a readable format"""
    if not result or not result.get("success"):
        print("❌ Failed to retrieve plans")
        if result and "error" in result:
            print(f"   Error: {result['error']}")
        return
    
    data = result.get("data", [])
    if not data:
        print("⚠️ No plans found")
        return
    
    print(f"✅ Found {len(data)} travel insurance plans:")
    print("\n" + "="*80)
    
    for i, plan in enumerate(data, 1):
        print(f"{i:2d}. {plan.get('nome', 'N/A')}")
        print(f"    ID: {plan.get('id', 'N/A')} | Price: ${plan.get('preco', 'N/A')} | Family: ${plan.get('precofamiliar', 'N/A')}")
        print(f"    Max Age: {plan.get('idademaximaparaemissao', 'N/A')} | Max Duration: {plan.get('vigenciamaxima', 'N/A')} days")
        
        if i % 5 == 0 and i < len(data):  # Add separator every 5 plans
            print("    " + "-"*70)


def test_environment(client: CorisClient, env_name: str, use_prod: bool) -> Dict[str, bool]:
    """Test all functionality in a specific environment"""
    print(f"\n{'='*20} Testing {env_name} Environment {'='*20}")
    
    results = {}
    
    # Test 1: Login validation
    print(f"\n🔑 Test 1: User Login Validation")
    login_result = client.validate_access(use_prod=use_prod)
    results['login'] = login_result and login_result.get("success", False)
    display_login_result(login_result)
    
    # Test 2: Access verification
    print(f"\n🔍 Test 2: Access Verification")
    verify_result = client.verify_access(use_prod=use_prod)
    results['verify'] = verify_result and verify_result.get("success", False)
    if verify_result and verify_result.get("success"):
        print("✅ Access verification successful!")
    else:
        print("❌ Access verification failed")
        if verify_result and "error" in verify_result:
            print(f"   Error: {verify_result['error']}")
    
    # Test 3: Search travel plans
    print(f"\n🌎 Test 3: Search Travel Plans")
    plans_result = client.search_plans(use_prod=use_prod)
    results['plans'] = plans_result and plans_result.get("success", False)
    display_plans_result(plans_result)
    
    return results


def main():
    """Main test function"""
    print("🔐 CORIS API Test Suite")
    print("="*80)
    
    try:
        # Initialize client
        client = CorisClient()
        print(f"Initialized client for user: {client.login}")
        
        # Test both environments
        homolog_results = test_environment(client, "HOMOLOG", use_prod=False)
        prod_results = test_environment(client, "PRODUCTION", use_prod=True)
        
        # Overall summary
        print("\n" + "="*80)
        print("📊 OVERALL SUMMARY")
        print("="*80)
        
        print("HOMOLOG Environment:")
        print(f"  Login Validation:     {'✅ PASS' if homolog_results.get('login') else '❌ FAIL'}")
        print(f"  Access Verification:  {'✅ PASS' if homolog_results.get('verify') else '❌ FAIL'}")
        print(f"  Plans Search:         {'✅ PASS' if homolog_results.get('plans') else '❌ FAIL'}")
        
        print("\nPRODUCTION Environment:")
        print(f"  Login Validation:     {'✅ PASS' if prod_results.get('login') else '❌ FAIL'}")
        print(f"  Access Verification:  {'✅ PASS' if prod_results.get('verify') else '❌ FAIL'}")
        print(f"  Plans Search:         {'✅ PASS' if prod_results.get('plans') else '❌ FAIL'}")
        
        # Check if all tests passed
        all_homolog_passed = all(homolog_results.values())
        all_prod_passed = all(prod_results.values())
        
        if all_homolog_passed and all_prod_passed:
            print("\n🎉 All tests passed in both environments! CORIS API is working correctly.")
        elif all_prod_passed:
            print("\n✅ All production tests passed! (Some homolog tests may have failed)")
        elif all_homolog_passed:
            print("\n⚠️ All homolog tests passed, but some production tests failed.")
        else:
            print("\n❌ Some tests failed. Check the details above.")
            
    except ValueError as e:
        print(f"❌ Configuration error: {e}")
        print("Please check your .env file and ensure all required variables are set.")
    except Exception as e:
        print(f"❌ Unexpected error: {e}")


if __name__ == "__main__":
    main()
