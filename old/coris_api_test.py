import os
import requests
from xml.etree import ElementTree as ET
from dotenv import load_dotenv

# Load environment variables
load_dotenv()
LOGIN = os.getenv("CORIS_LOGIN")
SENHA = os.getenv("CORIS_SENHA")
ENV = os.getenv("CORIS_ENV", "homolog")  # Default to homologation

# Validate credentials and environment
if not LOGIN or not SENHA:
    raise ValueError("CORIS_LOGIN or CORIS_SENHA is missing in .env file")
if ENV not in ["homolog", "prod"]:
    raise ValueError("CORIS_ENV must be 'homolog' or 'prod'")
print(f"Credentials: LOGIN='{LOGIN}' (len={len(LOGIN)}), SENHA='[REDACTED]' (len={len(SENHA)})")

# Set URLs based on environment
URLS = {
    "homolog": "https://ws.coris-homolog.com.br/webservice2/service.asmx",
    "prod": "https://ws.coris.com.br/webservice2/service.asmx"
}
BASE_URL = URLS[ENV]

# Helper function to create XML payload
def create_xml_payload(method, params):
    execute = ET.Element("execute")
    for name, type_, value in params:
        param = ET.SubElement(execute, "param")
        param.set("name", name)
        param.set("type", type_)
        param.set("value", str(value))
    xml = ET.tostring(execute, encoding="utf-8", method="xml").decode("utf-8")
    print(f"{method} Parameter Count: {len(params)}")
    return xml

# Helper function to make SOAP request
def soap_request(method, xml_payload):
    headers = {"Content-Type": "text/xml; charset=utf-8", "SOAPAction": f"http://tempuri.org/{method}"}
    soap_envelope = f"""<?xml version="1.0" encoding="utf-8"?>
<soap:Envelope xmlns:soap="http://schemas.xmlsoap.org/soap/envelope/">
    <soap:Body>
        <{method} xmlns="http://tempuri.org/">
            <xml>{xml_payload}</xml>
        </{method}>
    </soap:Body>
</soap:Envelope>"""
    print(f"\n{method} Request Payload:")
    print(soap_envelope)
    response = requests.post(BASE_URL, data=soap_envelope, headers=headers)
    print(f"\n{method} Response Info:")
    print(f"Status Code: {response.status_code}")
    print(f"Headers: {response.headers}")
    return response

# Test WSDL availability
def test_wsdl():
    wsdl_url = f"{BASE_URL}?WSDL"
    print(f"\nTesting WSDL for {ENV} at {wsdl_url}")
    try:
        response = requests.get(wsdl_url, timeout=5)
        print(f"Status Code: {response.status_code}")
        print(f"Headers: {response.headers}")
        if response.status_code == 200:
            try:
                ET.fromstring(response.text)
                print("WSDL is accessible and valid XML")
                print(f"Response Snippet: {response.text[:200]}...")
            except ET.ParseError:
                print("WSDL response is not valid XML")
        else:
            print(f"WSDL request failed with status: {response.status_code}")
    except requests.RequestException as e:
        print(f"Error accessing WSDL: {str(e)}")

# Test VerificaAcesso
def test_verifica_acesso(login=LOGIN, senha=SENHA):
    params = [
        ("login", "varchar", login),
        ("senha", "varchar", senha)
    ]
    xml_payload = create_xml_payload("VerificaAcesso", params)
    response = soap_request("VerificaAcesso", xml_payload)
    
    print("\nVerifica Acesso Raw Response:")
    print(response.text)
    
    try:
        root = ET.fromstring(response.text)
        result_elem = root.find(".//{http://tempuri.org/}VerificaAcessoResult")
        
        if result_elem is None:
            print("Error: VerificaAcessoResult element not found")
            return
        
        result_text = result_elem.text
        if not result_text:
            print("Error: VerificaAcessoResult is empty")
            return
        
        try:
            result_root = ET.fromstring(result_text)
            result_status = result_root.find("result")
            detail = result_root.find("detail")
            result_status_text = result_status.text if result_status is not None else "Not found"
            detail_text = detail.text if detail is not None else "No detail provided"
            
            erro = result_root.find("Erro")
            erro_text = erro.text if erro is not None else "Not found"
            
            print("\nVerifica Acesso Result:")
            print(f"Result Status: {result_status_text}")
            print(f"Detail: {detail_text}")
            print(f"Erro: {erro_text}")
            
            if erro_text == "OK":
                print("Acesso verificado com sucesso")
            else:
                print("Erro ao verificar acesso")
        except ET.ParseError:
            print("\nVerifica Acesso Result (Manual Parse):")
            if "<result>" in result_text:
                status_start = result_text.index("<result>") + 8
                status_end = result_text.index("</result>")
                result_status = result_text[status_start:status_end]
                print(f"Result Status: {result_status}")
            else:
                print("Result Status: Not found")
                
            if "<detail>" in result_text:
                detail_start = result_text.index("<detail>") + 8
                detail_end = result_text.index("</detail>")
                detail_text = result_text[detail_start:detail_end]
                print(f"Detail: {detail_text}")
            else:
                print("Detail: Not found")
                
            if "<Erro>" in result_text:
                erro_start = result_text.index("<Erro>") + 6
                erro_end = result_text.index("</Erro>")
                erro_text = result_text[erro_start:erro_end]
                print(f"Erro: {erro_text}")
            else:
                print("Erro: Not found")
                
    except ET.ParseError:
        print("Error: Failed to parse SOAP response")
    except AttributeError as e:
        print(f"Error: Attribute error - {str(e)}")

# Test validarAcesso
def test_validar_acesso(login=LOGIN, senha=SENHA):
    params = [
        ("login", "varchar", login),
        ("senha", "varchar", senha),
        ("cartao", "char", "M")
    ]
    xml_payload = create_xml_payload("validarAcesso", params)
    response = soap_request("validarAcesso", xml_payload)
    
    print("\nValidar Acesso Raw Response:")
    print(response.text)
    
    try:
        root = ET.fromstring(response.text)
        result_elem = root.find(".//{http://tempuri.org/}validarAcessoResult")
        
        if result_elem is None:
            print("Error: validarAcessoResult element not found")
            return
        
        result_text = result_elem.text
        if not result_text:
            print("Error: validarAcessoResult is empty")
            return
        
        try:
            result_root = ET.fromstring(result_text)
            result_status = result_root.find("result").text
            detail = result_root.find("detail")
            detail_text = detail.text if detail is not None else "No detail provided"
            
            print("\nValidar Acesso Result:")
            print(f"Result Status: {result_status}")
            print(f"Detail: {detail_text}")
            
            if result_status == "NOK":
                return
                
            erro = result_root.find(".//erro")
            if erro is None:
                print("Error: erro element not found")
                return
                
            erro_text = erro.text
            print(f"Erro: {erro_text}")
            if erro_text == "0":
                login = result_root.find(".//login").text
                idcliente = result_root.find(".//idcliente").text
                print(f"Login: {login}, ID Cliente: {idcliente}")
            else:
                print("Acesso inválido")
        except ET.ParseError:
            print("\nValidar Acesso Result (Manual Parse):")
            if "<result>" in result_text:
                status_start = result_text.index("<result>") + 8
                status_end = result_text.index("</result>")
                result_status = result_text[status_start:status_end]
                print(f"Result Status: {result_status}")
            else:
                print("Result Status: Not found")
                
            if "<detail>" in result_text:
                detail_start = result_text.index("<detail>") + 8
                detail_end = result_text.index("</detail>")
                detail_text = result_text[detail_start:detail_end]
                print(f"Detail: {detail_text}")
            else:
                print("Detail: Not found")
                
    except ET.ParseError:
        print("Error: Failed to parse SOAP response")
    except AttributeError as e:
        print(f"Error: Attribute error - {str(e)}")

# Test simplified validarAcesso (without cartao)
def test_validar_acesso_simplified(login=LOGIN, senha=SENHA):
    params = [
        ("login", "varchar", login),
        ("senha", "varchar", senha)
    ]
    xml_payload = create_xml_payload("validarAcesso", params)
    response = soap_request("validarAcesso", xml_payload)
    
    print("\nSimplified Validar Acesso Raw Response:")
    print(response.text)
    
    try:
        root = ET.fromstring(response.text)
        result_elem = root.find(".//{http://tempuri.org/}validarAcessoResult")
        
        if result_elem is None:
            print("Error: validarAcessoResult element not found")
            return
        
        result_text = result_elem.text
        if not result_text:
            print("Error: validarAcessoResult is empty")
            return
        
        try:
            result_root = ET.fromstring(result_text)
            result_status = result_root.find("result").text
            detail = result_root.find("detail")
            detail_text = detail.text if detail is not None else "No detail provided"
            
            print("\nSimplified Validar Acesso Result:")
            print(f"Result Status: {result_status}")
            print(f"Detail: {detail_text}")
        except ET.ParseError:
            print("\nSimplified Validar Acesso Result (Manual Parse):")
            if "<result>" in result_text:
                status_start = result_text.index("<result>") + 8
                status_end = result_text.index("</result>")
                result_status = result_text[status_start:status_end]
                print(f"Result Status: {result_status}")
            else:
                print("Result Status: Not found")
                
            if "<detail>" in result_text:
                detail_start = result_text.index("<detail>") + 8
                detail_end = result_text.index("</detail>")
                detail_text = result_text[detail_start:detail_end]
                print(f"Detail: {detail_text}")
            else:
                print("Detail: Not found")
                
    except ET.ParseError:
        print("Error: Failed to parse SOAP response")
    except AttributeError as e:
        print(f"Error: Attribute error - {str(e)}")

# Test BuscarPlanosNovosV13
def test_buscar_planos(login=LOGIN, senha=SENHA):
    params = [
        ("login", "varchar", login),
        ("senha", "varchar", senha),
        ("destino", "int", 1),
        ("vigencia", "int", 1),
        ("home", "int", 0),
        ("multi", "int", 0)
    ]
    xml_payload = create_xml_payload("BuscarPlanosNovosV13", params)
    response = soap_request("BuscarPlanosNovosV13", params)
    
    print("\nBuscar Planos Raw Response:")
    print(response.text)
    
    try:
        root = ET.fromstring(response.text)
        result_elem = root.find(".//{http://tempuri.org/}BuscarPlanosNovosV13Result")
        
        if result_elem is None:
            print("Error: BuscarPlanosNovosV13Result element not found")
            return
        
        result_text = result_elem.text
        if not result_text:
            print("Error: BuscarPlanosNovosV13Result is empty")
            return
        
        try:
            result_root = ET.fromstring(result_text)
            result_status = result_root.find("result").text
            detail = result_root.find("detail")
            detail_text = detail.text if detail is not None else "No detail provided"
            
            print("\nBuscar Planos Result:")
            print(f"Result Status: {result_status}")
            print(f"Detail: {detail_text}")
            
            if result_status == "NOK":
                return
                
            erro = result_root.find(".//erro")
            if erro is None:
                print("Error: erro element not found")
                return
                
            erro_text = erro.text
            print(f"Erro: {erro_text}")
            if erro_text == "0":
                for plano in result_root.findall(".//Table"):
                    id_plano = plano.find("id").text
                    nome = plano.find("nome").text
                    preco = plano.find("preco").text
                    print(f"Plano ID: {id_plano}, Nome: {nome}, Preço: {preco}")
            else:
                print("Erro ao buscar planos")
        except ET.ParseError:
            print("\nBuscar Planos Result (Manual Parse):")
            if "<result>" in result_text:
                status_start = result_text.index("<result>") + 8
                status_end = result_text.index("</result>")
                result_status = result_text[status_start:status_end]
                print(f"Result Status: {result_status}")
            else:
                print("Result Status: Not found")
                
            if "<detail>" in result_text:
                detail_start = result_text.index("<detail>") + 8
                detail_end = result_text.index("</detail>")
                detail_text = result_text[detail_start:detail_end]
                print(f"Detail: {detail_text}")
            else:
                print("Detail: Not found")
                
    except ET.ParseError:
        print("Error: Failed to parse SOAP response")
    except AttributeError as e:
        print(f"Error: Attribute error - {str(e)}")

# Test with example credentials (webmod, 123456)
def test_with_example_credentials():
    print("\nTesting with example credentials (webmod, 123456)")
    test_verifica_acesso(login="webmod", senha="123456")
    test_validar_acesso(login="webmod", senha="123456")
    test_validar_acesso_simplified(login="webmod", senha="123456")
    test_buscar_planos(login="webmod", senha="123456")

if __name__ == "__main__":
    test_wsdl()
    test_verifica_acesso()
    test_validar_acesso()
    test_validar_acesso_simplified()
    test_buscar_planos()
    test_with_example_credentials()
