import os
import requests
import html
from xml.etree import ElementTree as ET
from dotenv import load_dotenv

load_dotenv()

USER = os.getenv("CORIS_LOGIN")
PASS = os.getenv("CORIS_SENHA")
URLS = [os.getenv("URL_HOMO"), os.getenv("URL_PROD")]

# Validate credentials
if not USER or not PASS:
    raise ValueError("CORIS_LOGIN or CORIS_SENHA is missing in .env file")
if not URLS[0] or not URLS[1]:
    raise ValueError("URL_HOMO or URL_PROD is missing in .env file")

def build_xml_params(params: dict) -> str:
    """Build XML parameter string for the strXML parameter"""
    inner = "\n".join(
        f"<param name='{k}' type='{t}' value='{v}' />"
        for k, (t, v) in params.items()
    )
    return f"<execute>{inner}</execute>"

def build_soap_body(method: str, xml_params: str) -> str:
    """Build SOAP envelope for a specific method"""
    escaped_xml = html.escape(xml_params)
    return f"""<?xml version="1.0" encoding="utf-8"?>
<soap:Envelope xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
               xmlns:xsd="http://www.w3.org/2001/XMLSchema"
               xmlns:soap="http://schemas.xmlsoap.org/soap/envelope/">
  <soap:Body>
    <{method} xmlns="http://tempuri.org/">
      <strXML>{escaped_xml}</strXML>
    </{method}>
  </soap:Body>
</soap:Envelope>"""

def parse_coris_response(response_text: str, method: str) -> dict:
    """Parse CORIS SOAP response and extract meaningful data"""
    try:
        # Parse the SOAP envelope
        root = ET.fromstring(response_text)

        # Find the result element
        result_elem = root.find(f".//{{http://tempuri.org/}}{method}Result")
        if result_elem is None:
            return {"error": "Result element not found", "raw": response_text}

        # The result contains escaped XML, so we need to unescape it
        result_xml = html.unescape(result_elem.text)

        # Remove XML declaration if present (it causes parsing issues)
        if result_xml.startswith('<?xml'):
            result_xml = result_xml[result_xml.find('?>') + 2:].strip()

        # Wrap in a root element since the XML may have multiple root elements
        wrapped_xml = f"<root>{result_xml}</root>"

        # Parse the inner XML
        inner_root = ET.fromstring(wrapped_xml)

        # Extract result status
        result_status = inner_root.find("result")
        status = result_status.text if result_status is not None else "UNKNOWN"

        parsed_data = {
            "status": status,
            "success": status == "OK",
            "data": []
        }

        # Extract table data if present
        table = inner_root.find("table")
        if table is not None:
            for row in table.findall("row"):
                row_data = {}
                for column in row.findall("column"):
                    name = column.get("name")
                    value = column.text
                    row_data[name] = value
                parsed_data["data"].append(row_data)

        return parsed_data

    except ET.ParseError as e:
        return {"error": f"XML parsing error: {e}", "raw": response_text}
    except Exception as e:
        return {"error": f"Unexpected error: {e}", "raw": response_text}

def call_method(method: str, params: dict, verbose: bool = False):
    """Call a specific SOAP method with parameters"""
    xml_params = build_xml_params(params)
    body = build_soap_body(method, xml_params)
    headers = {
        "Content-Type": "text/xml; charset=utf-8",
        "SOAPAction": f"\"http://tempuri.org/{method}\""
    }

    for url in URLS:
        env_name = "HOMOLOG" if "homolog" in url else "PROD"
        print(f"-- Calling {method} on {env_name}")

        try:
            resp = requests.post(url, data=body, headers=headers, timeout=30)
            resp.raise_for_status()

            # Parse the response
            parsed = parse_coris_response(resp.text, method)

            if parsed.get("success"):
                print("✅ Success!")
                if verbose:
                    print(f"Raw response: {resp.text}")
                return parsed
            else:
                print(f"❌ API Error: {parsed.get('error', 'Unknown error')}")
                if verbose:
                    print(f"Raw response: {resp.text}")

        except requests.HTTPError as e:
            print(f"❌ HTTP Error: {e}")
            if verbose:
                print(f"Response: {resp.text}")
                print(f"Status: {resp.status_code}")
        except Exception as e:
            print(f"❌ Unexpected error: {e}")

    print("‼️ Failed on both environments.")
    return None

def display_login_result(result: dict):
    """Display login result in a readable format"""
    if not result or not result.get("success"):
        print("❌ Login failed")
        return

    data = result.get("data", [])
    if data:
        user_data = data[0]
        print(f"✅ Login successful!")
        print(f"   User: {user_data.get('login', 'N/A')}")
        print(f"   Client ID: {user_data.get('idcliente', 'N/A')}")
        print(f"   Error Code: {user_data.get('erro', 'N/A')}")

def display_plans_result(result: dict):
    """Display plans result in a readable format"""
    if not result or not result.get("success"):
        print("❌ Failed to retrieve plans")
        return

    data = result.get("data", [])
    if not data:
        print("⚠️ No plans found")
        return

    print(f"✅ Found {len(data)} travel insurance plans:")
    print("\n" + "="*80)
    for i, plan in enumerate(data, 1):
        print(f"{i:2d}. {plan.get('nome', 'N/A')}")
        print(f"    ID: {plan.get('id', 'N/A')} | Price: ${plan.get('preco', 'N/A')} | Family: ${plan.get('precofamiliar', 'N/A')}")
        print(f"    Max Age: {plan.get('idademaximaparaemissao', 'N/A')} | Max Duration: {plan.get('vigenciamaxima', 'N/A')} days")
        if i % 5 == 0 and i < len(data):  # Add separator every 5 plans
            print("    " + "-"*70)

def main():
    print(f"🔐 Testing CORIS API with credentials: {USER} / {'*' * len(PASS) if PASS else 'None'}")
    print("="*80)

    # Test 1: Login validation
    print("\n🔑 Test 1: User Login Validation (validarAcesso)")
    result1 = call_method("validarAcesso", {
        "login": ("varchar", USER),
        "senha": ("varchar", PASS),
        "cartao": ("char", "M")
    })
    display_login_result(result1)

    # Test 2: Access verification (alternative method)
    print("\n🔍 Test 2: Access Verification (VerificaAcesso)")
    result2 = call_method("VerificaAcesso", {
        "login": ("varchar", USER),
        "senha": ("varchar", PASS)
    })
    if result2 and result2.get("success"):
        print("✅ Access verification successful!")
    else:
        print("❌ Access verification failed")

    # Test 3: Search travel plans
    print("\n🌎 Test 3: Search Travel Plans (BuscarPlanosNovosV13)")
    result3 = call_method("BuscarPlanosNovosV13", {
        "login": ("varchar", USER),
        "senha": ("varchar", PASS),
        "destino": ("int", "1"),  # Latin America
        "vigencia": ("int", "1"),  # Single trip
        "home": ("int", "0"),     # Not home country
        "multi": ("int", "0")     # Not multi-trip
    })
    display_plans_result(result3)

    # Summary
    print("\n" + "="*80)
    print("📊 SUMMARY")
    print("="*80)
    print(f"Login Validation:     {'✅ PASS' if result1 and result1.get('success') else '❌ FAIL'}")
    print(f"Access Verification:  {'✅ PASS' if result2 and result2.get('success') else '❌ FAIL'}")
    print(f"Plans Search:         {'✅ PASS' if result3 and result3.get('success') else '❌ FAIL'}")

    if all(r and r.get("success") for r in [result1, result2, result3]):
        print("\n🎉 All tests passed! CORIS API is working correctly.")
    else:
        print("\n⚠️ Some tests failed. Check the details above.")

if __name__ == "__main__":
    main()

