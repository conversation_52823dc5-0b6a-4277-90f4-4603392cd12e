import os
import requests
from dotenv import load_dotenv

load_dotenv()

USER = os.getenv("USER")
PASS = os.getenv("PASS")
URLS = [os.getenv("URL_HOMO"), os.getenv("URL_PROD")]

HEADERS = {
    "Content-Type": "text/xml; charset=utf-8",
    "SOAPAction": "\"http://tempuri.org/execute\""
}

def build_execute_body(params: dict) -> str:
    inner = "\n".join(
        f"<param name='{k}' type='{t}' value='{v}' />"
        for k, (t, v) in params.items()
    )
    # Sem indentação ou quebra antes do <?xml
    return f"""<?xml version="1.0" encoding="utf-8"?>
<soap:Envelope xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
               xmlns:xsd="http://www.w3.org/2001/XMLSchema"
               xmlns:soap="http://schemas.xmlsoap.org/soap/envelope/">
  <soap:Body>
    <execute xmlns="http://tempuri.org/">
      {inner}
    </execute>
  </soap:Body>
</soap:Envelope>"""

def call(params):
    body = build_execute_body(params)
    for url in URLS:
        print(f"-- Chamando execute em {url}")
        resp = requests.post(url, data=body, headers=HEADERS, timeout=30)
        try:
            resp.raise_for_status()
            print(resp.text)
            return
        except requests.HTTPError as e:
            print(f"   ❌ {e}\n{resp.text}")
    print("‼️ Falhou nos dois ambientes.")

def main():
    # Exemplo de chamada validarAcesso
    call({
        "login": ("varchar", USER),
        "senha": ("varchar", PASS),
        "cartao": ("char", "M")
    })
    # Exemplo de chamada BuscarPlanosNovosV13
    call({
        "login": ("varchar", USER),
        "senha": ("varchar", PASS),
        "destino": ("int", "1"),
        "vigencia": ("int", "1"),
        "home": ("int", "0"),
        "multi": ("int", "0")
    })

if __name__ == "__main__":
    main()

